# Go binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
main
bin/
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
*.coverage

# Go workspace file
go.work

# Dependency directories
vendor/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment variables (but keep the example)
.env
.env.local
.env.production
!.env.example

# Log files
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Air live reload
tmp/

# Generated files (keep api/generated as it's needed)
# api/generated/

# Docker
.dockerignore

# Build artifacts
build/

# Test coverage
coverage.html
coverage.xml

# Documentation
docs/build/

# Certificates
*.pem
*.key
*.crt
*.cert

# Configuration files with sensitive data
config.local.yml
config.production.yml 