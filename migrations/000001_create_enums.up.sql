-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable PostGIS extension for geospatial data
CREATE EXTENSION IF NOT EXISTS postgis;

-- User enums
CREATE TYPE user_role AS ENUM ('user', 'admin');
CREATE TYPE user_status AS ENUM ('active', 'suspended', 'pending');

-- Japanese plate types
CREATE TYPE plate_type AS ENUM ('normal', 'commercial', 'rental', 'military');

-- Parking lot status
CREATE TYPE lot_status AS ENUM ('active', 'maintenance', 'closed');

-- Session status
CREATE TYPE session_status AS ENUM ('active', 'completed', 'cancelled', 'error');

-- Payment status
CREATE TYPE payment_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'refunded');

-- Booking status
CREATE TYPE booking_status AS ENUM ('confirmed', 'cancelled', 'completed', 'no_show');

-- Notification types
CREATE TYPE notification_type AS ENUM (
    'parked', 'payment_completed', 'overstay_warning', 
    'price_alert', 'booking_reminder'
);

-- Detection direction
CREATE TYPE detection_direction AS ENUM ('entry', 'exit'); 