# Application Environment
ENVIRONMENT=development

# Server Configuration
PORT=8080
READ_TIMEOUT=30
WRITE_TIMEOUT=30
IDLE_TIMEOUT=120
MAX_HEADER_BYTES=1048576

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=root
DB_NAME=smooth_parking
DB_SSL_MODE=disable
DB_TIMEZONE=Asia/Tokyo
DB_MAX_IDLE_CONNS=10
DB_MAX_OPEN_CONNS=100
DB_CONN_MAX_LIFETIME=60

# JWT Configuration
JWT_SECRET_KEY=your-secret-key
JWT_EXPIRY_HOURS=24
JWT_REFRESH_DAYS=7
JWT_ISSUER=smooth-parking

# Stripe Configuration
STRIPE_SECRET_KEY=
STRIPE_PUBLISHABLE_KEY=
STRIPE_WEBHOOK_SECRET=

# Email Configuration
EMAIL_PROVIDER=smtp
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_FROM=
EMAIL_FROM_NAME=Smooth Parking
EMAIL_TLS=true

# AWS Configuration
AWS_REGION=ap-northeast-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=
AWS_S3_REGION=ap-northeast-1
